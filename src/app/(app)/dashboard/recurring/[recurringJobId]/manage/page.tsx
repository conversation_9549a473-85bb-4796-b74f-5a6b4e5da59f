"use client";
import { use, useEffect, useState } from "react";
import { useGetRecurring } from "@/app/services/recurring.hook";
import AlertManager from "@/app/components/AlertManager/AlertManager";

export default function SingleRecurringJobPage({
  params,
}: {
  params: Promise<{ recurringJobId: string }>;
}) {
  const { recurringJobId } = use(params);
  const { data, refetch } = useGetRecurring();
  const [currentRecurring, setCurrentRecurring] = useState(null);

  useEffect(() => {
    if (recurringJobId && data) {
      const currentRecurring = data.find(
        (recurring) => recurring.id === recurringJobId
      );
      setCurrentRecurring(currentRecurring);
    }
  }, [recurringJobId, data]);

  return (
    <AlertManager isRecipe={false} refetch={refetch} data={currentRecurring} />
  );
}

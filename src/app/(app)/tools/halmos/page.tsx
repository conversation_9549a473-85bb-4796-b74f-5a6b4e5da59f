import type { Metadata } from "next";
import { FuzzerType } from "@/app/app.constants";
import ToolPageLayout from "../../components/tool-layout";

export const metadata: Metadata = {
  title: "Halmos Logs Parser",
  description:
    "Paste Halmos Logs and automatically convert broken properties into Foundry Repros",
};

export default function HalmosParserPage() {
  return (
    <ToolPageLayout
      toolType={FuzzerType.HALMOS}
      toolName="Halmos Logs Scraper"
      toolDescription={[
        "This tool allows to scrape halmos logs for broken properties repros",
        "Paste your raw halmos logs, and the tool will generate foundry repros for you",
        "NOTE: You must pass -vv verbosity and clean the logs with ansifilter or it won't work!",
        "e.g halmos --contract HalmosDirect -vv | ansifilter > out.txt",
        "NOTE: Struct definitions are not supported yet, just fix the import and Struct Type Name!",
      ]}
      youtubeUrl="https://www.youtube.com/embed/WDdFVZpTAZo"
      youtubeOverlayText="Learn how to use Halmos"
    />
  );
}

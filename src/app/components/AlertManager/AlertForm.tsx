import React from "react";
import { AppButton } from "../app-button";
import { AppInput } from "../app-input";
import { Body2, Title2Strong } from "../app-typography";

// Helper component to reduce complexity
const TelegramSection: React.FC<{
  isEditing: boolean;
  showTGAlert: boolean;
  currentTelegramUsername: string;
  isButtonLoading: boolean;
  onToggleTGAlert: () => void;
  onTelegramUsernameChange: (value: string) => void;
  onTestTelegramUsername: () => void;
}> = ({
  isEditing,
  showTGAlert,
  currentTelegramUsername,
  isButtonLoading,
  onToggleTGAlert,
  onTelegramUsernameChange,
  onTestTelegramUsername,
}) => (
  <div className="space-y-2">
    {!isEditing && (
      <AppButton
        variant={showTGAlert ? "primary" : "outline"}
        size="sm"
        onClick={onToggleTGAlert}
      >
        {!showTGAlert ? "Add Telegram" : "Remove Telegram"}
      </AppButton>
    )}

    {(showTGAlert || isEditing) && (
      <div className="space-y-2">
        <AppInput
          type="text"
          label="Telegram Username"
          placeholder="your_username"
          value={currentTelegramUsername || ""}
          onChange={(e) => onTelegramUsernameChange(e.target.value)}
        />

        <div className="space-y-1">
          <Body2 color="tertiary" className="text-sm">
            • Case sensitive, no @ symbol needed
          </Body2>
          <Body2 color="tertiary" className="text-sm">
            • You must start a chat with our bot first
          </Body2>
        </div>

        {currentTelegramUsername && (
          <div className="flex flex-col gap-2">
            <a
              rel="noreferrer"
              target="_blank"
              href={`https://t.me/GetRecon_bot`}
              className="text-accent-primary underline transition-colors hover:text-accent-alt-primary"
            >
              → Start chat with @GetRecon_bot
            </a>

            <AppButton
              variant="outline"
              size="sm"
              onClick={onTestTelegramUsername}
              disabled={isButtonLoading || !currentTelegramUsername}
            >
              {isButtonLoading ? "Testing..." : "Test Connection"}
            </AppButton>
          </div>
        )}
      </div>
    )}
  </div>
);

interface AlertFormProps {
  isEditingAlert: {
    alertId: string;
    threshold: number;
    webhookUrl: string;
    editing: boolean;
    telegramHandle?: string;
  };
  alertThreshold: number;
  alertWebhookUrl: string;
  telegramUsername: string;
  showWebhookAlert: boolean;
  showTGAlert: boolean;
  isButtonLoading: boolean;
  onThresholdChange: (value: number) => void;
  onWebhookUrlChange: (value: string) => void;
  onTelegramUsernameChange: (value: string) => void;
  onToggleWebhookAlert: () => void;
  onToggleTGAlert: () => void;
  onTestTelegramUsername: () => void;
  onCreateAlert: () => void;
  onUpdateAlert: () => void;
  onCancelEdit: () => void;
}

export const AlertForm: React.FC<AlertFormProps> = ({
  isEditingAlert,
  alertThreshold,
  alertWebhookUrl,
  telegramUsername,
  showWebhookAlert,
  showTGAlert,
  isButtonLoading,
  onThresholdChange,
  onWebhookUrlChange,
  onTelegramUsernameChange,
  onToggleWebhookAlert,
  onToggleTGAlert,
  onTestTelegramUsername,
  onCreateAlert,
  onUpdateAlert,
  onCancelEdit,
}) => {
  const currentThreshold = isEditingAlert.editing
    ? isEditingAlert.threshold
    : alertThreshold;
  const currentWebhookUrl = isEditingAlert.editing
    ? isEditingAlert.webhookUrl
    : alertWebhookUrl;
  const currentTelegramUsername = isEditingAlert.editing
    ? isEditingAlert.telegramHandle
    : telegramUsername;

  const canCreateAlert =
    currentThreshold > 0 && (currentWebhookUrl || currentTelegramUsername);

  return (
    <div className="flex w-2/5 flex-col gap-4">
      <div className="space-y-2">
        <Title2Strong color="primary">
          {isEditingAlert.editing ? "Edit Alert" : "Create New Alert"}
        </Title2Strong>
        <Body2 color="secondary">
          {isEditingAlert.editing
            ? "Modify your alert settings below"
            : "Set up notifications for when issues are found"}
        </Body2>
      </div>

      {/* Threshold Section */}
      <div className="space-y-2">
        <AppInput
          type="text"
          label="Alert Threshold"
          placeholder="Enter number of issues (e.g., 1)"
          value={currentThreshold.toString()}
          onChange={(e) => {
            const value = e.target.value.replace(/\D/g, "");
            if (value === "" || (!isNaN(Number(value)) && Number(value) >= 0)) {
              onThresholdChange(value === "" ? 0 : Number(value));
            }
          }}
        />
        <Body2 color="tertiary" className="text-sm">
          Number of issues that will trigger an alert
        </Body2>
      </div>

      {/* Notification Methods Section */}
      <div className="space-y-3">
        <div className="space-y-2">
          <Body2 color="primary" className="font-medium">
            Notification Methods
          </Body2>
          <Body2 color="tertiary" className="text-sm">
            Choose at least one way to receive alerts
          </Body2>
        </div>

        {/* Webhook Option */}
        <div className="space-y-2">
          {!isEditingAlert.editing && (
            <AppButton
              variant={showWebhookAlert ? "primary" : "outline"}
              size="sm"
              onClick={onToggleWebhookAlert}
            >
              {!showWebhookAlert ? "Add Webhook" : "Remove Webhook"}
            </AppButton>
          )}

          {(showWebhookAlert || isEditingAlert.editing) && (
            <div className="space-y-1">
              <AppInput
                type="text"
                label="Webhook URL"
                placeholder="https://your-webhook-url.com/alerts"
                value={currentWebhookUrl}
                onChange={(e) => onWebhookUrlChange(e.target.value)}
              />
              <Body2 color="tertiary" className="text-sm">
                HTTP endpoint that will receive POST requests with alert data
              </Body2>
            </div>
          )}
        </div>

        {/* Telegram Option */}
        <TelegramSection
          isEditing={isEditingAlert.editing}
          showTGAlert={showTGAlert}
          currentTelegramUsername={currentTelegramUsername}
          isButtonLoading={isButtonLoading}
          onToggleTGAlert={onToggleTGAlert}
          onTelegramUsernameChange={onTelegramUsernameChange}
          onTestTelegramUsername={onTestTelegramUsername}
        />
      </div>

      {/* Action Buttons */}
      <div className="space-y-3 border-t border-stroke-neutral-decorative pt-2">
        {!canCreateAlert && !isEditingAlert.editing && (
          <div className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-tertiary p-3">
            <Body2 color="tertiary" className="text-sm">
              ⚠️ Please set a threshold greater than 0 and choose at least one
              notification method
            </Body2>
          </div>
        )}

        {isEditingAlert.editing ? (
          <div className="flex gap-3">
            <AppButton
              onClick={onUpdateAlert}
              disabled={isButtonLoading || !canCreateAlert}
              className="flex-1"
            >
              {isButtonLoading ? "Updating..." : "Update Alert"}
            </AppButton>
            <AppButton
              variant="outline"
              onClick={onCancelEdit}
              disabled={isButtonLoading}
            >
              Cancel
            </AppButton>
          </div>
        ) : (
          <AppButton
            onClick={onCreateAlert}
            disabled={isButtonLoading || !canCreateAlert}
            size="lg"
            fullWidth
          >
            {isButtonLoading ? "Creating..." : "Create Alert"}
          </AppButton>
        )}
      </div>
    </div>
  );
};

import { useState } from "react";
import axios from "axios";
import { toast } from "sonner";

export const useAlertCreation = (
  isRecipe: boolean,
  refetch: () => void,
  testTelegramUsername: (username: string) => Promise<string | undefined>
) => {
  const [isButtonLoading, setIsButtonLoading] = useState(false);

  const recipeAlertCreationHandler = async (
    recipeId: string,
    alertThreshold: number,
    alertWebhookUrl: string,
    telegramUsername: string
  ) => {
    setIsButtonLoading(true);

    if (!telegramUsername && alertWebhookUrl === "") {
      toast.error("Please fill either TG Username or Webhook URL");
      setIsButtonLoading(false);
      return false;
    }

    try {
      let createdAlert;

      if (telegramUsername) {
        const chatId = await testTelegramUsername(telegramUsername);
        if (!chatId) {
          setIsButtonLoading(false);
          return false;
        }

        createdAlert = await axios({
          method: "POST",
          url: `/api/alertRecipeCreate`,
          data: {
            webhookUrl: alertWebhookUrl,
            threshold: alertThreshold,
            recipeId,
            telegramUsername,
            chatId,
          },
        });

        if (createdAlert.status === 200) {
          await axios({
            method: "POST",
            url: `/api/telegram/sendMessage`,
            data: {
              chatId,
              text: `You have successfully subscribed to alerts for recipe ${recipeId}
You set a threshold of ${alertThreshold} alert${alertThreshold > 1 ? "s" : ""}`,
            },
          });
        }
      } else {
        createdAlert = await axios({
          method: "POST",
          url: `/api/alertRecipeCreate`,
          data: {
            webhookUrl: alertWebhookUrl,
            threshold: alertThreshold,
            recipeId,
          },
        });
      }

      if (createdAlert.status === 200) {
        refetch();
        toast.success("Alert created");
        return true;
      } else {
        toast.error("Couldn't create alert");
        return false;
      }
    } catch (error) {
      toast.error("Couldn't create alert");
      return false;
    } finally {
      setIsButtonLoading(false);
    }
  };

  const recurringAlertCreationHandler = async (
    recurringJobId: string,
    alertThreshold: number,
    alertWebhookUrl: string,
    telegramUsername: string
  ) => {
    setIsButtonLoading(true);

    try {
      let createdAlert;

      if (telegramUsername) {
        const chatId = await testTelegramUsername(telegramUsername);
        if (!chatId) {
          setIsButtonLoading(false);
          return false;
        }

        createdAlert = await axios({
          method: "POST",
          url: `/api/alertRecurringCreate`,
          data: {
            webhookUrl: alertWebhookUrl,
            threshold: alertThreshold,
            recurringJobId,
            telegramUsername,
            chatId,
          },
        });

        if (createdAlert.status === 200) {
          await axios({
            method: "POST",
            url: `/api/telegram/sendMessage`,
            data: {
              chatId,
              text: `You have successfully subscribed to alerts for ${
                isRecipe ? "recipe" : "recurring job"
              } ${recurringJobId}
You set a threshold of ${alertThreshold} alert${alertThreshold > 1 ? "s" : ""}`,
            },
          });
        }
      } else {
        createdAlert = await axios({
          method: "POST",
          url: `/api/alertRecipeCreate`,
          data: {
            webhookUrl: alertWebhookUrl,
            threshold: alertThreshold,
            recurringJobId,
          },
        });
      }

      if (createdAlert.status === 200) {
        refetch();
        toast.success("Alert created");
        return true;
      } else {
        toast.error("Couldn't create alert");
        return false;
      }
    } catch (error) {
      toast.error("Couldn't create alert");
      return false;
    } finally {
      setIsButtonLoading(false);
    }
  };

  const alertCreationHandler = isRecipe
    ? recipeAlertCreationHandler
    : recurringAlertCreationHandler;

  return {
    isButtonLoading,
    alertCreationHandler,
  };
};

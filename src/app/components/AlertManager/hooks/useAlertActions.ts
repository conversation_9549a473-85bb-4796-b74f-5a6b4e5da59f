import { useState } from "react";
import axios from "axios";
import { toast } from "sonner";
import type { EditingAlert } from "../types";

export const useAlertActions = (refetch: () => void) => {
  const [isButtonLoading, setIsButtonLoading] = useState(false);

  const toggleAlertHandler = async (alertId: string) => {
    setIsButtonLoading(true);
    try {
      const response = await axios({
        method: "POST",
        url: `/api/alertToggle`,
        data: { alertId },
      });

      if (response.status === 200) {
        toast.success("Alert status toggled");
        refetch();
      } else {
        toast.error("Couldn't change alert status");
      }
    } catch (error) {
      toast.error("Couldn't change alert status");
    } finally {
      setIsButtonLoading(false);
    }
  };

  const deleteAlertHandler = async (alertId: string) => {
    setIsButtonLoading(true);
    try {
      const response = await axios({
        method: "POST",
        url: `/api/alertDelete`,
        data: { alertId },
      });

      if (response.status === 200) {
        toast.success("Alert deleted");
        refetch();
      } else {
        toast.error("Couldn't delete alert");
      }
    } catch (error) {
      toast.error("Couldn't delete alert");
    } finally {
      setIsButtonLoading(false);
    }
  };

  const testTelegramUsername = async (TGuserName: string) => {
    setIsButtonLoading(true);
    try {
      const telegramChatId = await axios({
        method: "POST",
        url: `/api/telegram/testChat`,
        data: { telegramUsername: TGuserName },
      });

      if (telegramChatId.status === 200) {
        toast.success("Telegram username is correct and chat is created");
      }
      return telegramChatId.data.data;
    } catch (err) {
      toast.error("Telegram username is incorrect or chat is not created");
      return undefined;
    } finally {
      setIsButtonLoading(false);
    }
  };

  const updateAlertHandler = async (isEditingAlert: EditingAlert) => {
    setIsButtonLoading(true);
    try {
      let response;
      if (isEditingAlert.telegramHandle) {
        const chatId = await testTelegramUsername(
          isEditingAlert.telegramHandle
        );
        if (!chatId) {
          setIsButtonLoading(false);
          return;
        }
        response = await axios({
          method: "POST",
          url: `/api/alertUpdate`,
          data: {
            alertId: isEditingAlert.alertId,
            threshold: isEditingAlert.threshold,
            webhookUrl: isEditingAlert.webhookUrl,
            telegramHandle: isEditingAlert.telegramHandle,
            chatId: chatId,
          },
        });
      } else {
        response = await axios({
          method: "POST",
          url: `/api/alertUpdate`,
          data: {
            alertId: isEditingAlert.alertId,
            threshold: isEditingAlert.threshold,
            webhookUrl: isEditingAlert.webhookUrl,
          },
        });
      }

      if (response.status === 200) {
        toast.success("Alert updated");
        refetch();
        return true;
      }
    } catch (error) {
      toast.error("Failed to update alert");
    } finally {
      setIsButtonLoading(false);
    }
    return false;
  };

  return {
    isButtonLoading,
    toggleAlertHandler,
    deleteAlertHandler,
    testTelegramUsername,
    updateAlertHandler,
  };
};

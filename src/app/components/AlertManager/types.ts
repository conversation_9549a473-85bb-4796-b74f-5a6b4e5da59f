export interface AlertManagerProps {
  isRecipe: boolean;
  refetch: () => void;
  data: any; // can be either recipe or recurring data
}

export interface EditingAlert {
  alertId: string;
  threshold: number;
  webhookUrl: string;
  editing: boolean;
  telegramHandle?: string;
}

export interface Alert {
  id: string;
  threshold: number;
  webhookUrl: string;
  telegramHandle?: string;
  active: boolean;
}

import React, { useState } from "react";
import { AppCode } from "../app-code";
import { AppPageTitle } from "../app-page-title";
import { AppButton } from "../app-button";
import { Body2 } from "../app-typography";
import { AlertForm } from "./AlertForm";
import { AlertList } from "./AlertList";
import { useAlertActions } from "./hooks/useAlertActions";
import { useAlertCreation } from "./hooks/useAlertCreation";
import type { AlertManagerProps, EditingAlert } from "./types";

export default function AlertManager({
  isRecipe,
  refetch,
  data,
}: AlertManagerProps) {
  const [alertThreshold, setAlertThreshold] = useState<number>(1);
  const [alertWebhookUrl, setAlertWebhookUrl] = useState<string>("");
  const [showAlertField, setShowAlertField] = useState<boolean>(false);
  const [isEditingAlert, setIsEditingAlert] = useState<EditingAlert>({
    alertId: "",
    threshold: 0,
    webhookUrl: "",
    editing: false,
  });
  const [telegramUsername, setTelegramUsername] = useState<string>("");
  const [showTGAlert, setShowTGAlert] = useState<boolean>(false);
  const [showWebhookAlert, setShowWebhookAlert] = useState<boolean>(false);

  // Use custom hooks for alert actions and creation
  const {
    isButtonLoading: actionsLoading,
    toggleAlertHandler,
    deleteAlertHandler,
    testTelegramUsername,
    updateAlertHandler,
  } = useAlertActions(refetch);

  const { isButtonLoading: creationLoading, alertCreationHandler } =
    useAlertCreation(isRecipe, refetch, testTelegramUsername);

  const isButtonLoading = actionsLoading || creationLoading;

  // Helper functions for form handling
  const handleCreateAlert = async () => {
    const success = await alertCreationHandler(
      data.id,
      alertThreshold,
      alertWebhookUrl,
      telegramUsername
    );

    if (success) {
      setAlertThreshold(1);
      setAlertWebhookUrl("");
      setTelegramUsername("");
    }
  };

  const handleUpdateAlert = async () => {
    const success = await updateAlertHandler(isEditingAlert);
    if (success) {
      setIsEditingAlert({
        alertId: "",
        threshold: 0,
        webhookUrl: "",
        editing: false,
        telegramHandle: "",
      });
    }
  };

  const editAlertHandler = (alert: any) => {
    setIsEditingAlert({
      alertId: alert.id,
      threshold: alert.threshold,
      webhookUrl: alert.webhookUrl,
      editing: true,
      telegramHandle: alert.telegramHandle,
    });
    setShowAlertField(true);
  };

  const setTelegramUsernameHandler = (value: string) => {
    if (value.startsWith("@")) {
      const trimmed = value.replace("@", "");
      setTelegramUsername(trimmed);
    } else {
      setTelegramUsername(value);
    }
  };

  const handleCancelEdit = () => {
    setIsEditingAlert({
      alertId: "",
      threshold: 0,
      webhookUrl: "",
      editing: false,
      telegramHandle: "",
    });
  };

  const handleThresholdChange = (value: number) => {
    if (isEditingAlert.editing) {
      setIsEditingAlert({
        ...isEditingAlert,
        threshold: value,
      });
    } else {
      setAlertThreshold(value);
    }
  };

  const handleWebhookUrlChange = (value: string) => {
    if (isEditingAlert.editing) {
      setIsEditingAlert({
        ...isEditingAlert,
        webhookUrl: value,
      });
    } else {
      setAlertWebhookUrl(value);
    }
  };

  const handleTelegramUsernameChange = (value: string) => {
    if (isEditingAlert.editing) {
      setIsEditingAlert({
        ...isEditingAlert,
        telegramHandle: value,
      });
    } else {
      setTelegramUsernameHandler(value);
    }
  };

  return (
    <div className="m-auto min-h-screen w-full grow overflow-y-auto bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="mb-[45px] pl-[45px] pt-[45px]">
        <AppPageTitle>
          {isRecipe ? "Recipe" : "Recurring"} Alert Management
        </AppPageTitle>
      </div>
      {data ? (
        <div className="m-auto w-4/5">
          <AppCode code={JSON.stringify(data, null, 2)} language="json" />
          <div className="mt-6 w-full">
            <div className="mb-5 flex w-full flex-row justify-between">
              <AppButton onClick={() => setShowAlertField(!showAlertField)}>
                Manage alerts
              </AppButton>
            </div>

            {!isButtonLoading && showAlertField ? (
              <div className="flex w-full flex-row justify-between">
                <AlertForm
                  isEditingAlert={isEditingAlert}
                  alertThreshold={alertThreshold}
                  alertWebhookUrl={alertWebhookUrl}
                  telegramUsername={telegramUsername}
                  showWebhookAlert={showWebhookAlert}
                  showTGAlert={showTGAlert}
                  isButtonLoading={isButtonLoading}
                  onThresholdChange={handleThresholdChange}
                  onWebhookUrlChange={handleWebhookUrlChange}
                  onTelegramUsernameChange={handleTelegramUsernameChange}
                  onToggleWebhookAlert={() =>
                    setShowWebhookAlert(!showWebhookAlert)
                  }
                  onToggleTGAlert={() => setShowTGAlert(!showTGAlert)}
                  onTestTelegramUsername={() =>
                    testTelegramUsername(telegramUsername)
                  }
                  onCreateAlert={handleCreateAlert}
                  onUpdateAlert={handleUpdateAlert}
                  onCancelEdit={handleCancelEdit}
                />

                <AlertList
                  alerts={data.alerts}
                  isButtonLoading={isButtonLoading}
                  onDeleteAlert={deleteAlertHandler}
                  onToggleAlert={toggleAlertHandler}
                  onEditAlert={editAlertHandler}
                />
              </div>
            ) : null}
            {isButtonLoading && (
              <Body2 color="primary">Performing action ...</Body2>
            )}
          </div>
        </div>
      ) : (
        ""
      )}
    </div>
  );
}

"use client";

import { AppBackButton } from "@/app/components/app-back-button";
import { AppCode } from "@/app/components/app-code";
import { AppButton } from "../app-button";
import { Title2Strong, Body2, Body1 } from "../app-typography";
import Link from "next/link";
import { FaGithub } from "react-icons/fa";
import { cn } from "@/lib/utils";
import { MainContentWrapper } from "@/app/(app)/components/main-content-wrapper";

interface InstallationStepData {
  id: number;
  title: string;
  subtitle?: string;
  code: string;
  language: "bash" | "toml";
}

const INSTALLATION_STEPS: InstallationStepData[] = [
  {
    id: 1,
    title: "Install Chimera & Setup Helpers",
    code: "forge install Recon-Fuzz/chimera Recon-Fuzz/setup-helpers --no-commit",
    language: "bash",
  },
  {
    id: 2,
    title: "Add Chimera & Recon Setup Helpers To Mappings",
    subtitle: "For example, change the remappings to",
    code: `remappings = [
    'forge-std/=lib/forge-std/src/',
    '@chimera/=lib/chimera/src/',
    '@recon/=lib/setup-helpers/src/'
  ]`,
    language: "toml",
  },
];

const SIMPLE_STEPS = [
  "3. Add all Recon Files To /test",
  "4. Move `medusa.json` and `echidna.yaml` to the project root",
  "5. Manually Fix the Constructors",
  "You're done!",
] as const;

const COMMAND_STEPS = [
  {
    id: 6,
    title: "Run Medusa with:",
    code: "medusa fuzz",
    language: "bash" as const,
  },
  {
    id: 7,
    title: "Run Echidna with:",
    code: "echidna . --contract CryticTester --config echidna.yaml",
    language: "bash" as const,
  },
] as const;

const GITHUB_TEMPLATE_URL = "https://github.com/Recon-Fuzz/create-chimera-app";

// Reusable components
interface InstallationStepProps {
  step: InstallationStepData;
  className?: string;
}

function InstallationStep({ step, className }: InstallationStepProps) {
  return (
    <div className={cn("mb-4", className)}>
      <Body2 color="primary" className="mb-2">
        {step.id}. {step.title}
      </Body2>
      {step.subtitle && (
        <Body1 color="secondary" className="mb-4">
          {step.subtitle}
        </Body1>
      )}
      <div className="mb-4">
        <AppCode code={step.code} language={step.language} />
      </div>
    </div>
  );
}

interface CommandStepProps {
  step: (typeof COMMAND_STEPS)[number];
  className?: string;
}

function CommandStep({ step, className }: CommandStepProps) {
  return (
    <div className={cn("mb-4", className)}>
      <Body2 color="primary" className="mb-2">
        {step.id}. {step.title}
      </Body2>
      <div className="mb-4">
        <AppCode code={step.code} language={step.language} />
      </div>
    </div>
  );
}

function SimpleStepsList() {
  return (
    <div className="mb-6">
      {SIMPLE_STEPS.map((text) => (
        <Body2 key={text} color="primary" className="mb-4">
          {text}
        </Body2>
      ))}
    </div>
  );
}

function BonusSection() {
  return (
    <Link href={GITHUB_TEMPLATE_URL} target="_blank" rel="noopener noreferrer">
      <div className="flex items-center gap-2">
        <Body2 color="primary">
          BONUS: Use the Create-Chimera-App Template
        </Body2>
        <AppButton leftIcon={<FaGithub />} size="sm">
          Github
        </AppButton>
      </div>
    </Link>
  );
}

export default function InstallationHelp() {
  return (
    <MainContentWrapper>
      <div className="p-11 lg:px-20">
        <Title2Strong color="primary" className="mb-5 flex items-center gap-2">
          <AppBackButton />
          Installation help
        </Title2Strong>

        {INSTALLATION_STEPS.map((step) => (
          <InstallationStep key={step.id} step={step} />
        ))}

        <SimpleStepsList />

        {COMMAND_STEPS.map((step) => (
          <CommandStep key={step.id} step={step} />
        ))}

        <BonusSection />
      </div>
    </MainContentWrapper>
  );
}

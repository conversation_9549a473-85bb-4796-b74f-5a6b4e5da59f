"use client";

import { useEffect, useState } from "react";

import type { FuzzerType } from "@/app/app.constants";
import type { GlobalVmConfig } from "@/app/utils/vm-config";
import { getCurrentVmConfig } from "@/app/utils/vm-config";

import { AppButton } from "./app-button";

interface VmButtonsProps {
  fuzzerType: FuzzerType;
  onLocalVmConfigChange?: (config: GlobalVmConfig) => void;
  className?: string;
}

export function VmButtons({
  fuzzerType,
  onLocalVmConfigChange,
  className = "",
}: VmButtonsProps) {
  const [vmConfig, setVmConfig] = useState<GlobalVmConfig>({
    prank: false,
    roll: false,
    time: false,
  });
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const currentConfig = getCurrentVmConfig(fuzzerType);
    const newGlobalConfig = {
      prank: currentConfig.prank,
      roll: currentConfig.roll,
      time: currentConfig.time,
    };
    setVmConfig(newGlobalConfig);
    setIsInitialized(true);
  }, [fuzzerType]);

  useEffect(() => {
    const handleConfigChange = (event: CustomEvent) => {
      const newConfig = event.detail as GlobalVmConfig;
      setVmConfig(newConfig);
    };

    const handleStorageChange = () => {
      const currentConfig = getCurrentVmConfig(fuzzerType);
      setVmConfig({
        prank: currentConfig.prank,
        roll: currentConfig.roll,
        time: currentConfig.time,
      });
    };

    window.addEventListener(
      "vm-config-changed",
      handleConfigChange as EventListener
    );
    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener(
        "vm-config-changed",
        handleConfigChange as EventListener
      );
      window.removeEventListener("storage", handleStorageChange);
    };
  }, [fuzzerType]);

  const handleToggle = (property: keyof GlobalVmConfig) => {
    const newConfig = {
      ...vmConfig,
      [property]: !vmConfig[property],
    };
    onLocalVmConfigChange(newConfig);
    setVmConfig(newConfig);
  };

  if (!isInitialized) {
    return (
      <div className={`flex gap-2 max-[900px]:hidden ${className}`}>
        <span className="text-sm text-fore-neutral-secondary">
          Loading VM options...
        </span>
      </div>
    );
  }

  return (
    <div className={`flex gap-2 max-[900px]:hidden ${className}`}>
      <AppButton
        key="prank"
        variant={vmConfig.prank ? "primary" : "outline"}
        size="sm"
        onClick={() => handleToggle("prank")}
      >
        <span>Use vm.prank</span>
      </AppButton>
      <AppButton
        key="roll"
        variant={vmConfig.roll ? "primary" : "outline"}
        size="sm"
        onClick={() => handleToggle("roll")}
      >
        <span>Use vm.roll</span>
      </AppButton>
      <AppButton
        key="time"
        variant={vmConfig.time ? "primary" : "outline"}
        size="sm"
        onClick={() => handleToggle("time")}
      >
        <span>Use vm.warp</span>
      </AppButton>
    </div>
  );
}
